import tkinter as tk
from tkinter import messagebox, ttk
import pyperclip

# 配色方案
COLOR_BG = "#F0F3F5"       # 浅灰背景
COLOR_PRIMARY = "#2B579A"  # 深微软蓝
COLOR_TEXT = "#FFFFFF"     # 白色文字
COLOR_INPUT_BG = "#FFFFFF" # 白色输入框
COLOR_LABEL = "#333333"    # 深灰标签

class MillimeterWaveUI:
    def __init__(self, main_root):
        self.main_root = main_root
        self.custom_font = ("微软雅黑", 11)
        
        self.mm_wave_root = tk.Toplevel(main_root)
        self.mm_wave_root.title("毫米波雷达解析器")
        self.mm_wave_root.configure(bg=COLOR_BG)

        # 输入区域
        input_frame = tk.Frame(self.mm_wave_root, bg=COLOR_BG)
        input_frame.pack(pady=10, padx=20, fill=tk.X)

        tk.Label(input_frame, text="输入16进制数据：", 
                bg=COLOR_BG, fg=COLOR_LABEL,
                font=self.custom_font).grid(row=0, column=0, padx=5, sticky='w')
        
        self.input_entry = tk.Entry(input_frame, width=40,
                                  bg=COLOR_INPUT_BG, fg=COLOR_LABEL,
                                  font=self.custom_font,
                                  relief=tk.FLAT)
        self.input_entry.grid(row=0, column=1, padx=5)

        self.mode_var = tk.StringVar(value="0x60B")
        mode_menu = ttk.Combobox(input_frame, textvariable=self.mode_var, 
                               values=["0x60B", "0x701"], width=6, state="readonly")
        mode_menu.grid(row=0, column=2, padx=5)

        # 操作按钮
        btn_frame = tk.Frame(input_frame, bg="#000080")
        btn_frame.grid(row=1, column=0, columnspan=3, pady=10)
        ttk.Button(btn_frame, text="粘贴数据", command=self.paste_from_clipboard, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="解析数据", command=self.parse_data, width=10).pack(side=tk.LEFT)

        # 结果展示区域
        result_frame = tk.Frame(self.mm_wave_root, bg=COLOR_BG)
        result_frame.pack(pady=15, padx=20)

        # 标签行
        labels = ["ID", "DistLat/m", "DistLong/m", "VLat/m/s", "VLong/m/s"]
        for col, text in enumerate(labels):
            tk.Label(result_frame, text=text, bg="#00BFFF", fg=COLOR_LABEL,
                    font=self.custom_font).grid(row=0, column=col, padx=8)

        # 单行数据框
        entry_style = {
            'bg': COLOR_INPUT_BG, 'fg': COLOR_LABEL,
            'font': self.custom_font, 'relief': tk.GROOVE,
            'width': 12, 'justify': tk.CENTER
        }
        self.id_entry = tk.Entry(result_frame, **entry_style)
        self.distlat_entry = tk.Entry(result_frame, **entry_style)
        self.distlong_entry = tk.Entry(result_frame, **entry_style)
        self.vlat_entry = tk.Entry(result_frame, **entry_style)
        self.vlong_entry = tk.Entry(result_frame, **entry_style)

        for col, entry in enumerate([self.id_entry, self.distlat_entry, 
                                   self.distlong_entry, self.vlat_entry, 
                                   self.vlong_entry]):
            entry.grid(row=1, column=col, padx=8, pady=5, ipady=3)

        self.mm_wave_root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def paste_from_clipboard(self):
        self.input_entry.delete(0, tk.END)
        self.input_entry.insert(0, pyperclip.paste())

    def parse_data(self):
        try:
            hex_data = self.input_entry.get().replace(" ", "")
            if len(hex_data) < 16: hex_data = hex_data.ljust(16, '0')
            binary = bin(int(hex_data, 16))[2:].zfill(64)
            
            # 公共解析
            id_val = int(binary[:8], 2)
            mode = self.mode_var.get()
            
            # 距离解析
            if mode == "0x60B":
                dist_long = int(binary[8:21], 2)*0.2 -500
                dist_lat = int(binary[21:32], 2)*0.2 -204.6
            else:  # 0x701
                dist_long = int(binary[8:21], 2)*0.2 -500
                dist_lat = int(binary[22:32], 2)*0.2 -102.3
            
            # 速度解析
            vlong_bits = binary[32:42]
            vlong = int(vlong_bits,2)*0.25 -128
            merged_vlat = binary[40:48]+binary[48:56]
            vlat = int(merged_vlat[3:12],2)*0.25 -64

            # 更新单行数据
            self.id_entry.delete(0, tk.END)
            self.id_entry.insert(0, id_val)
            self.distlat_entry.delete(0, tk.END)
            self.distlat_entry.insert(0, f"{dist_lat:.1f}")
            self.distlong_entry.delete(0, tk.END)
            self.distlong_entry.insert(0, f"{dist_long:.1f}")
            self.vlat_entry.delete(0, tk.END)
            self.vlat_entry.insert(0, f"{vlat:.2f}")
            self.vlong_entry.delete(0, tk.END)
            self.vlong_entry.insert(0, f"{vlong:.2f}")

        except Exception as e:
            messagebox.showerror("解析错误", f"无效数据格式\n{str(e)}")

    def on_closing(self):
        self.mm_wave_root.destroy()

def UI(main_root):
    MillimeterWaveUI(main_root)