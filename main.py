# main.py
import tkinter as tk
from tkinter import messagebox
import subprocess
import os
from config.UI import UI  # 导入毫米波雷达专属UI的函数
from config.UI2 import UI2  # 导入UI2的函数

# 创建主窗口实例
root = tk.Tk()
root.title("快捷工具")  # 设置窗口标题
root.geometry("250x300")  # 设置窗口大小为250x300像素

# 获取项目路径
base_dir = os.path.dirname(os.path.abspath(__file__))

# 显示签名
signature_label = tk.Label(root, text="潇沐制作", font=("Arial", 12, "italic"))
signature_label.pack(side=tk.TOP, anchor=tk.CENTER)  # 将签名标签放在窗口顶部居中

# 定义函数
def on_button1_click():
    # 检测目录是否存在
    if not os.path.exists(r'C:\Program Files (x86)\Kvaser\CanKing'):
        # 如果不存在则弹出警告对话框提示用户安装驱动
        messagebox.showwarning("注意", "请按照指引在默认路径安装驱动！")
        # 然后运行安装驱动的函数
        function1()
    else:
        # 如果存在则运行激光雷达的程序
        software_path = os.path.join(base_dir, 'config', 'LS32', 'LSC32_1212byte_v3.0.3_201116.exe')
        working_directory = os.path.join(base_dir, 'config', 'LS32')
        subprocess.run([software_path], cwd=working_directory)

# 创建按钮
button1 = tk.Button(root, text="激光雷达", command=on_button1_click)
button1.pack(pady=5)  # 设置垂直间距

# 定义函数
def on_button2_click():
    software_path = os.path.join(base_dir, 'config', 'Radar_Monitor_Release_V2.2', 'Radar_Monitor.exe')
    working_directory = os.path.join(base_dir, 'config', 'Radar_Monitor_Release_V2.2')
    subprocess.run([software_path], cwd=working_directory)

# 创建按钮
button2 = tk.Button(root, text="大陆雷达", command=on_button2_click)
button2.pack(pady=5)  # 设置垂直间距

# 定义函数
def on_button3_click():
    # 检测目录是否存在
    if not os.path.exists(r'C:\Program Files (x86)\USB_CAN TOOL'):
        # 如果不存在则弹出警告对话框提示用户安装驱动
        messagebox.showwarning("注意", "请按照指引在默认路径安装驱动！")
        # 然后运行安装驱动的函数
        function2()
    else:
        # 如果存在则运行毫米波雷达的程序
        software_path = os.path.join(base_dir, 'config', 'USB_CAN TOOL', 'USB_CAN_Tool.exe')
        working_directory = os.path.join(base_dir, 'config', 'USB_CAN TOOL')
        subprocess.run([software_path], cwd=working_directory)
        # 然后打开毫米波雷达的专属UI
        root.withdraw()  # 隐藏主窗口
        UI(root)  # 创建毫米波雷达的专属UI

# 创建按钮
button3 = tk.Button(root, text="毫米波雷达", command=on_button3_click)
button3.pack(pady=5)  # 设置垂直间距

# 定义函数
def on_button4_click():
    software_path = os.path.join(base_dir, 'config', 'RTKQC', 'RTKQC.exe')
    working_directory = os.path.join(base_dir, 'config', 'RTKQC')
    subprocess.run([software_path], cwd=working_directory)

# 创建按钮
button4 = tk.Button(root, text="惯导", command=on_button4_click)
button4.pack(pady=5)  # 设置垂直间距

# 定义函数
def on_button5_click():
    software_path = os.path.join(base_dir, 'config', 'CKTS.exe')
    working_directory = os.path.join(base_dir, 'config', 'RTKQC')
    subprocess.run([software_path], cwd=working_directory)

# 创建按钮
button5 = tk.Button(root, text="串口调试", command=on_button5_click)
button5.pack(pady=5)  # 设置垂直间距

# 定义函数
def on_button6_click():
    # 检测目录是否存在
    if not os.path.exists(r'C:\Program Files\Wireshark'):
        # 如果不存在则弹出警告对话框提示用户安装Wireshark
        messagebox.showwarning("注意", "请按照指引在默认路径安装Wireshark！")
        # 然后运行安装驱动的函数
        function3()
    else:
        # 如果存在则运行Wireshark的程序
        software_path = r'C:\Program Files\Wireshark\Wireshark.exe'
        subprocess.Popen([software_path])  # 使用Popen启动Wireshark
        print("Wireshark started, opening UI2 in 1 second...")
        root.after(1000, open_ui2)  # 延迟1秒后打开UI2界面

def open_ui2():
    print("Opening UI2...")
    root.withdraw()  # 隐藏主窗口
    UI2(root)  # 创建UI2界面

# 创建按钮
button6 = tk.Button(root, text="Wireshark", command=on_button6_click)
button6.pack(pady=5)  # 设置垂直间距

# 定义安装驱动的函数
def function1():
    software_path = os.path.join(base_dir, 'config', 'qudong.exe')
    working_directory = os.path.join(base_dir, 'config')
    subprocess.run([software_path], cwd=working_directory)

# 定义安装驱动的函数
def function2():
    software_path = os.path.join(base_dir, 'config', 'qudong2.exe')
    working_directory = os.path.join(base_dir, 'config')
    subprocess.run([software_path], cwd=working_directory)

# 定义安装驱动的函数
def function3():
    software_path = os.path.join(base_dir, 'config', 'qudong3.exe')
    working_directory = os.path.join(base_dir, 'config')
    subprocess.run([software_path], cwd=working_directory)

# 运行主窗口的主循环
root.mainloop()