# UI2.py
import tkinter as tk
from tkinter import scrolledtext
from tkinter import font
from . import lidar  # 使用相对导入

# 配色方案
COLOR_BG = "#FFFFFF"       # 背景白色
COLOR_PRIMARY = "#000080"  # 主色调蓝色
COLOR_TEXT = "#FFFFFF"     # 白色文字
COLOR_INPUT_BG = "#E3F2FD" # 输入框浅蓝背景
COLOR_TEXT_DARK = "#1E1E1E"# 深灰色文字
COLOR_BLUE_NAVY = "#00BFFF" 

def UI2(root):
    print("UI2 function called.")
    # 创建自定义字体
    custom_font = font.Font(family="微软雅黑", size=15)
    
    root.title("设备包与数据包解析")
    root.configure(bg=COLOR_BG)

    # 数据包输入框
    data_packet_label = tk.Label(root, text="请输入数据包", 
                               bg=COLOR_PRIMARY, fg=COLOR_TEXT, 
                               font=custom_font)
    data_packet_label.grid(row=0, column=0, columnspan=3, sticky="nsew", padx=5, pady=5)
    
    data_packet_entry = scrolledtext.ScrolledText(root, width=80, height=10,
                                                bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                                font=custom_font,
                                                insertbackground=COLOR_TEXT_DARK)
    data_packet_entry.grid(row=1, column=0, columnspan=3, sticky="w", padx=5, pady=5)

  
    # 切换按钮
    def switch_to_lidar():
        lidar_app = lidar.LidarViewer()
        lidar_app.mainloop()

    switch_button = tk.Button(root, text="激光雷达数据设备包解析", 
                            command=switch_to_lidar,
                            bg=COLOR_PRIMARY, fg=COLOR_TEXT,
                            activebackground="#005A9E", activeforeground=COLOR_TEXT,
                            font=custom_font, relief="flat")
    switch_button.grid(row=2, column=2, rowspan=2, sticky="nsew", padx=6, pady=5)
    # 数据块输入框
    data_block_label = tk.Label(root, text="请输入数据块1~12", 
                              bg=COLOR_BLUE_NAVY, fg=COLOR_TEXT_DARK,
                              font=custom_font)
    data_block_label.grid(row=2, column=0, sticky="nsew", padx=5, pady=5)
    
    data_block_entry = scrolledtext.ScrolledText(root, width=5, height=1,
                                               bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                               font=custom_font,
                                               insertbackground=COLOR_TEXT_DARK)
    data_block_entry.grid(row=3, column=0, sticky="nsew", padx=5, pady=5)

    # 通道输入框
    channel_label = tk.Label(root, text="请输入通道0~31", 
                           bg=COLOR_BLUE_NAVY, fg=COLOR_TEXT_DARK,
                           font=custom_font)
    channel_label.grid(row=2, column=1, sticky="nsew", padx=5, pady=5)
    
    channel_entry = scrolledtext.ScrolledText(root, width=5, height=1,
                                            bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                            font=custom_font,
                                            insertbackground=COLOR_TEXT_DARK)
    channel_entry.grid(row=3, column=1, sticky="nsew", padx=5, pady=5)

    # 解析按钮
    parse_button = tk.Button(root, text="解析", 
                           command=lambda: on_parse(data_packet_entry, data_block_entry, 
                                                   channel_entry, azimuth_entry, 
                                                   distance_entry, intensity_entry),
                           bg=COLOR_PRIMARY, fg=COLOR_TEXT,
                           activebackground="#005A9E", activeforeground=COLOR_TEXT,
                           font=custom_font, relief="flat")
    parse_button.grid(row=4, column=0, columnspan=3, sticky="nsew", padx=5, pady=5)

    # 解析结果输入框
    azimuth_label = tk.Label(root, text="方位角", 
                           bg=COLOR_BLUE_NAVY, fg=COLOR_TEXT_DARK,
                           font=custom_font)
    azimuth_label.grid(row=5, column=0, sticky="nsew", padx=5, pady=5)
    
    azimuth_entry = scrolledtext.ScrolledText(root, width=10, height=1,
                                           bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                           font=custom_font,
                                           insertbackground=COLOR_TEXT_DARK)
    azimuth_entry.grid(row=6, column=0, sticky="nsew", padx=5, pady=5)

    distance_label = tk.Label(root, text="距离", 
                            bg=COLOR_BLUE_NAVY, fg=COLOR_TEXT_DARK,
                            font=custom_font)
    distance_label.grid(row=5, column=1, sticky="nsew", padx=5, pady=5)
    
    distance_entry = scrolledtext.ScrolledText(root, width=10, height=1,
                                            bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                            font=custom_font,
                                            insertbackground=COLOR_TEXT_DARK)
    distance_entry.grid(row=6, column=1, sticky="nsew", padx=5, pady=5)

    intensity_label = tk.Label(root, text="强度", 
                             bg=COLOR_BLUE_NAVY, fg=COLOR_TEXT_DARK,
                             font=custom_font)
    intensity_label.grid(row=5, column=2, sticky="nsew", padx=5, pady=5)
    
    intensity_entry = scrolledtext.ScrolledText(root, width=10, height=1,
                                              bg=COLOR_INPUT_BG, fg=COLOR_TEXT_DARK,
                                              font=custom_font,
                                              insertbackground=COLOR_TEXT_DARK)
    intensity_entry.grid(row=6, column=2, sticky="nsew", padx=5, pady=5)



def on_parse(data_packet_entry, data_block_entry, channel_entry, azimuth_entry, distance_entry, intensity_entry):
    data_packet = data_packet_entry.get('1.0', tk.END).strip()
    
    # 排除无效数据
    valid_data = ""
    for line in data_packet.split("\n"):
        parts = line.split("   ")
        if len(parts) > 1:
            valid_data += parts[1].replace(" ", "")
    
    data_block = data_block_entry.get('1.0', tk.END).strip()
    channel = channel_entry.get('1.0', tk.END).strip()
    
    result = parse_data(valid_data, data_block, channel)
    
    azimuth_entry.delete('1.0', tk.END)
    distance_entry.delete('1.0', tk.END)
    intensity_entry.delete('1.0', tk.END)
    
    azimuth_entry.insert(tk.END, result.get('azimuth', '未找到'))
    distance_entry.insert(tk.END, result.get('distance', '未找到'))
    intensity_entry.insert(tk.END, result.get('intensity', '未找到'))

def parse_data(data_packet, data_block, channel):
    # 将输入的数据包转换为字符串列表，方便处理
    data_list = data_packet.replace(" ", "").replace("\n", "")
    
    # 定义包头的长度和数据块的分隔符
    packet_header_length = 84
    block_separator = "ffee"
    block_length = 4 + 4 + (32 * 6)  # 包括标志位ffee、方位角和32组通道数据
    
    # 计算数据块的起始位置
    block_start = packet_header_length
    
    # 初始化变量
    current_block = 1
    result = {}
    
    # 遍历数据包，寻找数据块和通道
    while block_start < len(data_list):
        if data_list[block_start:block_start+4] == block_separator:
            # 找到新的数据块
            if current_block == int(data_block):
                # 方位角在数据块的第5,6,7,8个16进制数
                azimuth = data_list[block_start+4:block_start+8]
                # 计算方位角
                azimuth = int(azimuth[2:4] + azimuth[0:2], 16) * 0.01
                result['azimuth'] = str(azimuth)
                
                # 计算通道数据的位置
                channel_start = block_start + 8 + (int(channel) * 6)
                if channel_start + 6 <= len(data_list):
                    # 距离和强度
                    distance = data_list[channel_start:channel_start+4]
                    # 计算距离
                    distance = int(distance[2:4] + distance[0:2], 16) * 0.25
                    result['distance'] = str(distance)
                    
                    intensity = data_list[channel_start+4:channel_start+6]
                    # 计算强度
                    intensity = int(intensity, 16)
                    result['intensity'] = str(intensity)
                break
            current_block += 1
            block_start += block_length
        else:
            block_start += 1  # 如果没有找到数据块分隔符，继续向后查找
    
    return result

if __name__ == "__main__":
    root = tk.Tk()
    UI2(root)
    root.mainloop()
