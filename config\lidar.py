import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog
import pyshark
import struct
import os
from datetime import datetime
import math
import threading

class LidarViewer(tk.Tk):
    def __init__(self):
        super().__init__()

        self.title("激光雷达数据解析软件")
        self.geometry("1200x800")

        # 创建主框架
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建控制面板
        self.control_frame = ttk.LabelFrame(self.main_frame, text="控制面板")
        self.control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 文件路径输入框和选择按钮
        self.path_frame = ttk.Frame(self.control_frame)
        self.path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.path_var = tk.StringVar(value="请选择PCAP文件...")
        self.path_entry = ttk.Entry(self.path_frame, textvariable=self.path_var, width=80)
        self.path_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        self.browse_button = ttk.Button(self.path_frame, text="浏览", command=self.browse_file)
        self.browse_button.pack(side=tk.LEFT)

        # 添加开始按钮
        self.start_button = ttk.Button(self.control_frame, text="开始解析", command=self.start_parsing)
        self.start_button.pack(pady=5)

        # 添加连接雷达按钮
        self.connect_button = ttk.Button(self.control_frame, text="连接雷达", command=self.connect_lidar)
        self.connect_button.pack(pady=5)

        # 添加回到顶部和到底部的按钮
        self.top_button = ttk.Button(self.control_frame, text="回到顶部", command=self.scroll_to_top)
        self.top_button.pack(side=tk.LEFT, padx=(5, 0))

        self.bottom_button = ttk.Button(self.control_frame, text="回到底部", command=self.scroll_to_bottom)
        self.bottom_button.pack(side=tk.LEFT, padx=(5, 0))

        # 创建数据显示区
        self.text_area = scrolledtext.ScrolledText(self.main_frame, height=40, font=('Courier', 10))
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=5)

        self.is_running = False  # 控制实时解析的标志


    def destroy(self):
        # 重写destroy方法，只关闭当前窗口
        self.withdraw()  # 隐藏窗口而不是销毁
        super().destroy()  # 调用父类的destroy方法

    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="选择PCAP文件",
            filetypes=[("PCAP files", "*.pcap"), ("All files", "*.*")]
        )
        if filename:
            self.path_var.set(filename)

    def log_message(self, message):
        """添加日志消息但不自动滚动到底部"""
        self.text_area.insert(tk.END, message + "\n")

    def format_table_row(self, *columns, widths=None):
        if widths is None:
            widths = [15] * len(columns)
        return "".join(str(col).ljust(width) for col, width in zip(columns, widths))

    def calculate_coordinates(self, distance, azimuth, elevation):
        """
        计算3D坐标(笛卡尔坐标系)
        distance: 距离(米)
        azimuth: 水平角度(度)
        elevation: 垂直角度(度)
        返回: (x, y, z) 坐标，单位为米
        """
        # 转换角度为弧度
        azimuth_rad = math.radians(azimuth)
        elevation_rad = math.radians(elevation)
        
        # 根据球坐标系转换公式计算
        x = distance * math.cos(elevation_rad) * math.cos(azimuth_rad)
        y = -distance * math.cos(elevation_rad) * math.sin(azimuth_rad)  # 负号是因为y轴方向定义
        z = distance * math.sin(elevation_rad)
        
        return x, y, z

    def parse_block_data(self, data, block_index, offset):
        """解析单个数据块的详细信息"""
        try:
            # 先获取所有数据块的方位角
            azimuth_angles = []
            temp_offset = 0
            for i in range(12):
                angle = int.from_bytes(data[temp_offset+2:temp_offset+4], byteorder='little')
                azimuth_angles.append(angle)
                temp_offset += 100

            # 获取当前数据块的方位角
            flag = int.from_bytes(data[offset:offset+2], byteorder='little')
            current_azimuth = azimuth_angles[block_index]
            
            # 获取下一个数据块的方位角
            next_azimuth = azimuth_angles[(block_index + 1) % 12]

            # 检查标志位的字节序
            if flag == 0xEEFF:  # 如果是反的，需要交换字节序
                flag = 0xFFEE
            
            self.log_message(f"\n[数据块 {block_index + 1}]")
            self.log_message(f"标志位: 0x{flag:04X} {'[正确]' if flag == 0xFFEE else '[错误]'}")
            self.log_message(f"数据块起始方位角: {current_azimuth/100.0:.2f}°")
            
            # 表头
            self.log_message("\n" + self.format_table_row(
                "通道", "距离(m)", "强度", "水平角度(°)", "X(m)", "Y(m)", "Z(m)",
                widths=[8, 12, 8, 15, 12, 12, 12]
            ))
            self.log_message("-" * 100)
            
            # 跳过标志位和方位角
            offset += 4
            
            # 处理32个通道数据
            for channel in range(32):
                distance = int.from_bytes(data[offset:offset+2], byteorder='little') * 0.0025
                intensity = data[offset+2]
                
                # 计算水平角度
                if next_azimuth < current_azimuth:
                    next_azimuth += 36000
                angle_diff = next_azimuth - current_azimuth
                channel_increment = angle_diff / 32.0
                horizontal_angle = (current_azimuth + (channel * channel_increment)) % 36000 / 100.0
                
                # 计算垂直角度（根据通道号）
                # 这里需要根据实际雷达的垂直角度分布进行调整
                vertical_angle = -15 + channel * 1.0  # 示例：假设从-15度开始，每个通道间隔1度
                
                # 计算XYZ坐标
                if distance > 0:  # 只计算有效距离的点
                    x, y, z = self.calculate_coordinates(distance, horizontal_angle, vertical_angle)
                    
                    self.log_message(self.format_table_row(
                        f"{channel:02d}",
                        f"{distance:.3f}",
                        f"{intensity}",
                        f"{horizontal_angle:.2f}",
                        f"{x:.3f}",
                        f"{y:.3f}",
                        f"{z:.3f}",
                        widths=[8, 12, 8, 15, 12, 12, 12]
                    ))
                else:
                    self.log_message(self.format_table_row(
                        f"{channel:02d}",
                        "0.000",
                        f"{intensity}",
                        f"{horizontal_angle:.2f}",
                        "0.000",
                        "0.000",
                        "0.000",
                        widths=[8, 12, 8, 15, 12, 12, 12]
                    ))
                
                offset += 3
            
            return offset
            
        except Exception as e:
            self.log_message(f"解析数据块 {block_index + 1} 时发生错误: {str(e)}")
            raise e

    def scroll_to_top(self):
        """滚动到顶部"""
        self.text_area.see("1.0")

    def scroll_to_bottom(self):
        """滚动到底部"""
        self.text_area.see(tk.END)

    def start_parsing(self):
        file_path = self.path_var.get()
        if not os.path.exists(file_path):
            self.log_message("错误：请选择有效的PCAP文件")
            return

        self.text_area.delete(1.0, tk.END)
        self.log_message(f"开始解析文件: {file_path}")
        self.log_message(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        total_packets = 0  # 初始化数据包计数
        try:
            cap = pyshark.FileCapture(
                file_path,
                display_filter="udp and ((udp.port == 2368) or (udp.port == 2369))",
                tshark_path="D:\\App\\Wireshark\\tshark.exe"
            )

            packet_count = 0
            for packet in cap:
                total_packets += 1  # 统计数据包总数
                try:
                    udp_payload = bytes.fromhex(packet.udp.payload.replace(':', ''))
                    
                    # 输出包的基本信息
                    self.log_message(f"\n\n{'='*100}")
                    self.log_message(f"数据包 {packet_count + 1}")
                    self.log_message(f"{'='*100}")
                    
                    # 网络信息
                    self.log_message("\n[网络信息]")
                    self.log_message(f"源IP地址: {packet.ip.src}")
                    self.log_message(f"目的IP地址: {packet.ip.dst}")
                    self.log_message(f"源端口: {packet.udp.srcport}")
                    self.log_message(f"目的端口: {packet.udp.dstport}")
                    
                    # 包结构信息
                    self.log_message("\n[包结构信息]")
                    self.log_message(f"以太网包头长度: 42 bytes")
                    self.log_message(f"UDP有效数据长度: {len(udp_payload)} bytes")
                    self.log_message(f"通道数据长度: 1200 bytes (12个数据块 × 100 bytes)")
                    self.log_message(f"附加信息长度: 6 bytes (时间戳4 bytes + 工厂信息2 bytes)")
                    self.log_message(f"总包长: {42 + len(udp_payload)} bytes")

                    if len(udp_payload) != 1206:
                        self.log_message(f"警告：UDP数据长度不正确，期望1206字节，实际{len(udp_payload)}字节")
                        continue

                    if int(packet.udp.dstport) == 2368:  # 数据包
                        # 解析12个数据块
                        offset = 0
                        for block in range(12):
                            offset = self.parse_block_data(udp_payload, block, offset)
                        
                        # 解析附加信息
                        timestamp = int.from_bytes(udp_payload[1200:1204], byteorder='little')
                        factory_info = udp_payload[1204]
                        factory_type = udp_payload[1205]
                        
                        self.log_message("\n[附加信息]")
                        self.log_message(f"时间戳: {timestamp} us")
                        self.log_message(f"回波信息: 0x{factory_info:02X} " + 
                                       f"({'最后回波' if factory_info == 0x38 else '最先回波' if factory_info == 0x37 else '双回波' if factory_info == 0x39 else '未知'})")
                        self.log_message(f"厂商信息: 0x{factory_type:02X} " + 
                                       f"({'C16雷达' if factory_type == 0x10 else 'C32雷达' if factory_type == 0x20 else '未知'})")
                        
                        packet_count += 1
                        
                        if packet_count >= 157:
                          break

                except Exception as e:
                    self.log_message(f"处理数据包时发生错误: {str(e)}")
                    raise e

            cap.close()
            self.log_message(f"\n总共处理了 {packet_count} 个数据包")
            self.log_message(f"PCAP文件中总共有 {total_packets} 个数据包")  # 显示总数据包数量
            self.log_message(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            self.log_message(f"发生错误: {e}")

    def connect_lidar(self):
        """连接激光雷达并开始实时解析数据"""
        self.is_running = True
        threading.Thread(target=self.real_time_parsing).start()  # 启动新线程进行实时解析

    def real_time_parsing(self):
        """实时解析数据的逻辑"""
        try:
            # 这里可以替换为实际的连接逻辑
            cap = pyshark.LiveCapture(interface='192.168.1.200', display_filter="udp and ((udp.port == 2368) or (udp.port == 2369))")
            for packet in cap.sniff_continuously():
                if not self.is_running:
                    break  # 如果停止运行，则退出循环

                # 解析数据包
                udp_payload = bytes.fromhex(packet.udp.payload.replace(':', ''))
                # 这里可以调用解析函数，类似于 start_parsing 中的逻辑
                self.process_packet(udp_payload)

        except Exception as e:
            self.log_message(f"实时解析时发生错误: {e}")

    def process_packet(self, udp_payload):
        """处理接收到的数据包"""
        # 这里可以添加解析逻辑，类似于 start_parsing 中的逻辑
        self.log_message(f"接收到数据包，长度: {len(udp_payload)} bytes")
        # 继续解析数据包...

    def stop_parsing(self):
        """停止实时解析"""
        self.is_running = False

if __name__ == "__main__":
    app = LidarViewer()
    app.mainloop()
